class StorageManager {
    constructor() {
        this.dbName = 'DNAEncryptionDB';
        this.dbVersion = 1;
        this.db = null;
    }

    async initialize() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                resolve();
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Create encryptions store
                if (!db.objectStoreNames.contains('encryptions')) {
                    const store = db.createObjectStore('encryptions', { keyPath: 'id', autoIncrement: true });
                    store.createIndex('timestamp', 'timestamp', { unique: false });
                    store.createIndex('fileName', 'fileName', { unique: false });
                }
            };
        });
    }

    async storeEncryption(data) {
        const transaction = this.db.transaction(['encryptions'], 'readwrite');
        const store = transaction.objectStore('encryptions');
        
        const encryptionData = {
            ...data,
            id: Date.now() + Math.random()
        };
        
        await store.add(encryptionData);
        return encryptionData.id;
    }

    async getEncryption(id) {
        const transaction = this.db.transaction(['encryptions'], 'readonly');
        const store = transaction.objectStore('encryptions');
        return await store.get(id);
    }

    async getAllEncryptions() {
        const transaction = this.db.transaction(['encryptions'], 'readonly');
        const store = transaction.objectStore('encryptions');
        return await store.getAll();
    }

    async deleteEncryption(id) {
        const transaction = this.db.transaction(['encryptions'], 'readwrite');
        const store = transaction.objectStore('encryptions');
        await store.delete(id);
    }
}