class DNACodec {
    constructor() {
        this.nucleotideMap = {
            '00': 'A', '01': 'T', '10': 'C', '11': 'G'
        };
        this.reverseMap = {
            'A': '00', 'T': '01', 'C': '10', 'G': '11'
        };
        this.compressionLevel = 9;
        this.errorCorrectionEnabled = true;
    }

    // Advanced Encoding with Error Correction
    encodeWithErrorCorrection(data) {
        const compressed = this.compress(data);
        const withChecksum = this.addChecksum(compressed);
        const withMetadata = this.embedMetadata(withChecksum);
        return this.binaryToDNA(withMetadata);
    }

    // Decoding with Error Correction
    decodeWithErrorCorrection(dnaSequence) {
        const binaryData = this.dnaToBinary(dnaSequence);
        const withoutMetadata = this.extractMetadata(binaryData);
        const verified = this.verifyChecksum(withoutMetadata);
        return this.decompress(verified);
    }

    // Binary to DNA conversion
    binaryToDNA(data) {
        const binaryString = Array.from(data)
            .map(byte => byte.toString(2).padStart(8, '0'))
            .join('');
        
        let dnaSequence = '';
        for (let i = 0; i < binaryString.length; i += 2) {
            const pair = binaryString.substr(i, 2).padEnd(2, '0');
            dnaSequence += this.nucleotideMap[pair];
        }
        return dnaSequence;
    }

    // DNA to Binary conversion
    dnaToBinary(dnaSequence) {
        let binaryString = '';
        for (const nucleotide of dnaSequence) {
            binaryString += this.reverseMap[nucleotide] || '00';
        }
        
        const bytes = [];
        for (let i = 0; i < binaryString.length; i += 8) {
            const byte = binaryString.substr(i, 8);
            if (byte.length === 8) {
                bytes.push(parseInt(byte, 2));
            }
        }
        return new Uint8Array(bytes);
    }

    // Compression using pako
    compress(data) {
        return pako.deflate(data, { level: this.compressionLevel });
    }

    decompress(data) {
        return pako.inflate(data);
    }

    // Add checksum for error detection
    addChecksum(data) {
        const checksum = this.calculateCRC32(data);
        const checksumBytes = new Uint8Array(4);
        new DataView(checksumBytes.buffer).setUint32(0, checksum);
        return new Uint8Array([...data, ...checksumBytes]);
    }

    // Verify checksum
    verifyChecksum(data) {
        if (data.length < 4) throw new Error('Invalid data: too short for checksum');
        
        const dataWithoutChecksum = data.slice(0, -4);
        const storedChecksum = new DataView(data.slice(-4).buffer).getUint32(0);
        const calculatedChecksum = this.calculateCRC32(dataWithoutChecksum);
        
        if (storedChecksum !== calculatedChecksum) {
            throw new Error('Checksum verification failed: data may be corrupted');
        }
        
        return dataWithoutChecksum;
    }

    // CRC32 calculation
    calculateCRC32(data) {
        const crcTable = this.generateCRC32Table();
        let crc = 0xFFFFFFFF;
        
        for (let i = 0; i < data.length; i++) {
            crc = (crc >>> 8) ^ crcTable[(crc ^ data[i]) & 0xFF];
        }
        
        return (crc ^ 0xFFFFFFFF) >>> 0;
    }

    generateCRC32Table() {
        const table = new Array(256);
        for (let i = 0; i < 256; i++) {
            let c = i;
            for (let j = 0; j < 8; j++) {
                c = (c & 1) ? (0xEDB88320 ^ (c >>> 1)) : (c >>> 1);
            }
            table[i] = c;
        }
        return table;
    }

    // Embed metadata
    embedMetadata(data) {
        const metadata = {
            timestamp: Date.now(),
            version: '2.0',
            originalSize: data.length
        };
        const metadataBytes = new TextEncoder().encode(JSON.stringify(metadata));
        return new Uint8Array([...metadataBytes, 0xFF, ...data]);
    }

    // Extract metadata
    extractMetadata(data) {
        const separatorIndex = data.indexOf(0xFF);
        if (separatorIndex === -1) {
            throw new Error('Invalid data format: metadata separator not found');
        }
        
        const metadataBytes = data.slice(0, separatorIndex);
        const actualData = data.slice(separatorIndex + 1);
        
        try {
            const metadata = JSON.parse(new TextDecoder().decode(metadataBytes));
            console.log('Extracted metadata:', metadata);
        } catch (error) {
            console.warn('Failed to parse metadata:', error);
        }
        
        return actualData;
    }

    // File type detection
    detectFileType(data) {
        const bytes = new Uint8Array(data);
        const signatures = {
            'image/jpeg': [0xFF, 0xD8, 0xFF],
            'image/png': [0x89, 0x50, 0x4E, 0x47],
            'application/pdf': [0x25, 0x50, 0x44, 0x46],
            'video/mp4': [0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70]
        };

        for (const [mimeType, signature] of Object.entries(signatures)) {
            if (this.matchesSignature(bytes, signature)) {
                return mimeType;
            }
        }
        return 'application/octet-stream';
    }

    matchesSignature(bytes, signature) {
        return signature.every((byte, index) => bytes[index] === byte);
    }
}