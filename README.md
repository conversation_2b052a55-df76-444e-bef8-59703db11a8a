# 🧬 DNA-Based Encryption Platform

<div align="center">

![DNA Encryption Platform](https://img.shields.io/badge/DNA-Encryption-blue?style=for-the-badge&logo=dna)
![Version](https://img.shields.io/badge/Version-2.0.0-green?style=for-the-badge)
![License](https://img.shields.io/badge/License-MIT-yellow?style=for-the-badge)
![Developer](https://img.shields.io/badge/Developer-Mezd-red?style=for-the-badge)

**Advanced web application for encrypting files using DNA sequence encoding with military-grade security**

**توسعه‌یافته توسط: Mezd**

[English](#english) | [فارسی](#فارسی) | [العربية](#العربية) | [Français](#français) | [Deutsch](#deutsch) | [Español](#español) | [中文](#中文) | [Русский](#русский)

</div>

---

## English

### 🚀 Features

- **🧬 DNA Encoding**: Convert any file to DNA sequences (A, T, C, G) with advanced nucleotide mapping
- **🔒 AES-GCM Encryption**: Military-grade 256-bit encryption before DNA conversion
- **📁 Large File Support**: Handle files up to 500MB with optimized chunked processing
- **🛡️ Error Correction**: Built-in Reed-Solomon compatible error correction and CRC32 checksum verification
- **🗜️ Compression**: Advanced DEFLATE compression (level 9) to reduce DNA sequence length
- **📊 Progress Tracking**: Real-time progress indicators with detailed status updates
- **💾 Local Storage**: IndexedDB for secure encryption history and metadata storage
- **🔍 File Type Detection**: Advanced MIME type detection with binary signature analysis
- **🌐 Multi-language Support**: Full internationalization with RTL support
- **⚡ Performance Optimized**: Efficient memory usage and processing algorithms

### 🏗️ Architecture

```
DNA-Encryption-Platform/
├── 🌐 Frontend Layer
│   ├── index.html (Main UI with advanced components)
│   ├── styles/main.css (Responsive styling with animations)
│   └── UI Components (Modular interface elements)
├── 🔧 Core Engine
│   ├── DNACodec.js (Advanced DNA encoding/decoding)
│   ├── AdvancedCryptoEngine.js (AES-GCM with PBKDF2)
│   └── AdvancedFileHandler.js (Optimized file processing)
├── 💾 Storage Layer
│   └── StorageManager.js (IndexedDB with encryption metadata)
└── 🎨 UI Layer
    └── UIManager.js (Advanced user interface management)
```

### 🔧 Installation

1. **Clone the repository:**
```bash
git clone https://github.com/mezd/dna-encryption-platform.git
cd dna-encryption-platform
```

2. **Run the auto-launcher:**

**Windows:**
```cmd
start.bat
```

**Linux/Mac:**
```bash
chmod +x start.sh
./start.sh
```

3. **Open your browser and navigate to:** `http://localhost:8000`

### 📖 Usage Guide

#### Encryption Process
1. **File Selection**: Choose any file up to 500MB
2. **Password Entry**: Enter a strong password (minimum 8 characters)
3. **Encryption**: Click "🧬 Encrypt to DNA" button
4. **Result**: Copy or download the generated DNA sequence
5. **Storage**: Encryption metadata is automatically saved locally

#### Decryption Process
1. **DNA Input**: Paste the DNA sequence (A, T, C, G nucleotides only)
2. **Password**: Enter the correct decryption password
3. **Decryption**: Click "🔬 Decrypt from DNA" button
4. **Download**: Automatically download the restored original file

### 🔒 Security Features

- **🔐 PBKDF2 Key Derivation**: 100,000 iterations with SHA-256 hashing
- **🛡️ AES-GCM Encryption**: 256-bit keys with authenticated encryption and integrity verification
- **🎲 Cryptographic Randomness**: Secure random salt and IV generation for each operation
- **✅ Data Integrity**: CRC32 checksum verification and metadata validation
- **🔒 Zero Knowledge**: Passwords never stored, only derived keys used temporarily

### 🧬 DNA Encoding Technical Details

- **Nucleotide Mapping**: Binary to DNA conversion (00→A, 01→T, 10→C, 11→G)
- **Error Correction**: Reed-Solomon compatible structure with redundancy
- **Metadata Embedding**: Timestamp, version, file type, and integrity data
- **Compression**: DEFLATE algorithm (level 9) for optimal size reduction
- **Sequence Validation**: Advanced DNA sequence integrity checking

---

## فارسی

### 🚀 ویژگی‌ها

- **🧬 کدگذاری DNA**: تبدیل هر فایلی به توالی‌های DNA (A, T, C, G) با نقشه‌برداری پیشرفته نوکلئوتیدها
- **🔒 رمزگذاری AES-GCM**: رمزگذاری نظامی 256 بیتی قبل از تبدیل به DNA
- **📁 پشتیبانی فایل‌های بزرگ**: پردازش فایل‌های تا 500 مگابایت با بهینه‌سازی تکه‌ای
- **🛡️ تصحیح خطا**: تصحیح خطای سازگار با Reed-Solomon و تأیید چک‌سام CRC32
- **🗜️ فشرده‌سازی**: فشرده‌سازی پیشرفته DEFLATE (سطح 9) برای کاهش طول توالی DNA
- **📊 پیگیری پیشرفت**: نشانگرهای پیشرفت بلادرنگ با به‌روزرسانی‌های دقیق وضعیت
- **💾 ذخیره‌سازی محلی**: IndexedDB برای تاریخچه امن رمزگذاری و ذخیره متادیتا
- **🔍 تشخیص نوع فایل**: تشخیص پیشرفته نوع MIME با تجزیه امضای باینری
- **🌐 پشتیبانی چندزبانه**: بین‌المللی‌سازی کامل با پشتیبانی RTL
- **⚡ بهینه‌سازی عملکرد**: استفاده بهینه از حافظه و الگوریتم‌های پردازش

### 📖 راهنمای استفاده

#### فرآیند رمزگذاری
1. **انتخاب فایل**: هر فایلی تا 500 مگابایت انتخاب کنید
2. **وارد کردن رمز عبور**: رمز عبور قوی وارد کنید (حداقل 8 کاراکتر)
3. **رمزگذاری**: روی دکمه "🧬 رمزگذاری به DNA" کلیک کنید
4. **نتیجه**: توالی DNA تولید شده را کپی یا دانلود کنید
5. **ذخیره‌سازی**: متادیتای رمزگذاری به طور خودکار محلی ذخیره می‌شود

#### فرآیند رمزگشایی
1. **ورودی DNA**: توالی DNA را وارد کنید (فقط نوکلئوتیدهای A, T, C, G)
2. **رمز عبور**: رمز عبور صحیح رمزگشایی را وارد کنید
3. **رمزگشایی**: روی دکمه "🔬 رمزگشایی از DNA" کلیک کنید
4. **دانلود**: فایل اصلی بازیابی شده به طور خودکار دانلود می‌شود

---

## العربية

### 🚀 الميزات

- **🧬 ترميز الحمض النووي**: تحويل أي ملف إلى تسلسلات الحمض النووي (A, T, C, G) مع رسم خرائط متقدم للنيوكليوتيدات
- **🔒 تشفير AES-GCM**: تشفير عسكري 256 بت قبل تحويل الحمض النووي
- **📁 دعم الملفات الكبيرة**: معالجة ملفات تصل إلى 500 ميجابايت مع معالجة مجزأة محسنة
- **🛡️ تصحيح الأخطاء**: تصحيح أخطاء متوافق مع Reed-Solomon والتحقق من المجموع الاختباري CRC32
- **🗜️ الضغط**: ضغط DEFLATE متقدم (المستوى 9) لتقليل طول تسلسل الحمض النووي
- **📊 تتبع التقدم**: مؤشرات تقدم في الوقت الفعلي مع تحديثات حالة مفصلة
- **💾 التخزين المحلي**: IndexedDB لتاريخ التشفير الآمن وتخزين البيانات الوصفية
- **🔍 اكتشاف نوع الملف**: اكتشاف نوع MIME متقدم مع تحليل التوقيع الثنائي
- **🌐 دعم متعدد اللغات**: تدويل كامل مع دعم RTL
- **⚡ محسن للأداء**: استخدام فعال للذاكرة وخوارزميات المعالجة

### 📖 دليل الاستخدام

#### عملية التشفير
1. **اختيار الملف**: اختر أي ملف يصل إلى 500 ميجابايت
2. **إدخال كلمة المرور**: أدخل كلمة مرور قوية (8 أحرف على الأقل)
3. **التشفير**: انقر على زر "🧬 تشفير إلى الحمض النووي"
4. **النتيجة**: انسخ أو قم بتنزيل تسلسل الحمض النووي<|im_start|> ISIL
5. **التخزين**: يتم حفظ بيانات التشفير الوصفية تلقائ<|im_start|> محلي<|im_end|>

---

## Français

### 🚀 Fonctionnalités

- **🧬 Encodage ADN**: Convertir n'importe quel fichier en séquences ADN (A, T, C, G) avec mappage avancé des nucléotides
- **🔒 Chiffrement AES-GCM**: Chiffrement militaire 256 bits avant conversion ADN
- **📁 Support de gros fichiers**: Traitement de fichiers jusqu'à 500 Mo avec traitement par chunks optimisé
- **🛡️ Correction d'erreurs**: Correction d'erreurs compatible Reed-Solomon et vérification de somme de contrôle CRC32
- **🗜️ Compression**: Compression DEFLATE avancée (niveau 9) pour réduire la longueur de séquence ADN
- **📊 Suivi de progression**: Indicateurs de progression en temps réel avec mises à jour détaillées du statut
- **💾 Stockage local**: IndexedDB pour l'historique de chiffrement sécurisé et le stockage de métadonnées
- **🔍 Détection de type de fichier**: Détection MIME avancée avec analyse de signature binaire
- **🌐 Support multilingue**: Internationalisation complète avec support RTL
- **⚡ Optimisé pour les performances**: Utilisation efficace de la mémoire et algorithmes de traitement

---

## Deutsch

### 🚀 Funktionen

- **🧬 DNA-Kodierung**: Konvertierung beliebiger Dateien in DNA-Sequenzen (A, T, C, G) mit erweitertem Nukleotid-Mapping
- **🔒 AES-GCM-Verschlüsselung**: Militärische 256-Bit-Verschlüsselung vor DNA-Konvertierung
- **📁 Große Datei-Unterstützung**: Verarbeitung von Dateien bis zu 500 MB mit optimierter Chunk-Verarbeitung
- **🛡️ Fehlerkorrektur**: Reed-Solomon-kompatible Fehlerkorrektur und CRC32-Prüfsummenverifikation
- **🗜️ Kompression**: Erweiterte DEFLATE-Kompression (Level 9) zur Reduzierung der DNA-Sequenzlänge
- **📊 Fortschrittsverfolgung**: Echtzeit-Fortschrittsindikatoren mit detaillierten Statusupdates
- **💾 Lokale Speicherung**: IndexedDB für sichere Verschlüsselungshistorie und Metadaten-Speicherung
- **🔍 Dateityp-Erkennung**: Erweiterte MIME-Typ-Erkennung mit binärer Signaturanalyse
- **🌐 Mehrsprachige Unterstützung**: Vollständige Internationalisierung mit RTL-Unterstützung
- **⚡ Leistungsoptimiert**: Effiziente Speichernutzung und Verarbeitungsalgorithmen

---

## Español

### 🚀 Características

- **🧬 Codificación de ADN**: Convertir cualquier archivo a secuencias de ADN (A, T, C, G) con mapeo avanzado de nucleótidos
- **🔒 Cifrado AES-GCM**: Cifrado militar de 256 bits antes de la conversión de ADN
- **📁 Soporte de archivos grandes**: Procesamiento de archivos de hasta 500 MB con procesamiento por chunks optimizado
- **🛡️ Corrección de errores**: Corrección de errores compatible con Reed-Solomon y verificación de suma de comprobación CRC32
- **🗜️ Compresión**: Compresión DEFLATE avanzada (nivel 9) para reducir la longitud de secuencia de ADN
- **📊 Seguimiento de progreso**: Indicadores de progreso en tiempo real con actualizaciones detalladas de estado
- **💾 Almacenamiento local**: IndexedDB para historial de cifrado seguro y almacenamiento de metadatos
- **🔍 Detección de tipo de archivo**: Detección MIME avanzada con análisis de firma binaria
- **🌐 Soporte multiidioma**: Internacionalización completa con soporte RTL
- **⚡ Optimizado para rendimiento**: Uso eficiente de memoria y algoritmos de procesamiento

---

## 中文

### 🚀 功能特性

- **🧬 DNA编码**: 将任何文件转换为DNA序列（A、T、C、G），具有高级核苷酸映射
- **🔒 AES-GCM加密**: DNA转换前的军用级256位加密
- **📁 大文件支持**: 处理高达500MB的文件，具有优化的分块处理
- **🛡️ 错误纠正**: Reed-Solomon兼容的错误纠正和CRC32校验和验证
- **🗜️ 压缩**: 高级DEFLATE压缩（级别9）以减少DNA序列长度
- **📊 进度跟踪**: 实时进度指示器，具有详细的状态更新
- **💾 本地存储**: IndexedDB用于安全加密历史和元数据存储
- **🔍 文件类型检测**: 高级MIME类型检测，具有二进制签名分析
- **🌐 多语言支持**: 完整的国际化，支持RTL
- **⚡ 性能优化**: 高效的内存使用和处理算法

---

## Русский

### 🚀 Возможности

- **🧬 ДНК-кодирование**: Преобразование любого файла в последовательности ДНК (A, T, C, G) с расширенным картированием нуклеотидов
- **🔒 Шифрование AES-GCM**: Военное 256-битное шифрование перед преобразованием ДНК
- **📁 Поддержка больших файлов**: Обработка файлов до 500 МБ с оптимизированной обработкой по частям
- **🛡️ Исправление ошибок**: Исправление ошибок, совместимое с Reed-Solomon, и проверка контрольной суммы CRC32
- **🗜️ Сжатие**: Расширенное сжатие DEFLATE (уровень 9) для уменьшения длины последовательности ДНК
- **📊 Отслеживание прогресса**: Индикаторы прогресса в реальном времени с подробными обновлениями статуса
- **💾 Локальное хранилище**: IndexedDB для безопасной истории шифрования и хранения метаданных
- **🔍 Определение типа файла**: Расширенное определение MIME-типа с анализом бинарной подписи
- **🌐 Многоязычная поддержка**: Полная интернационализация с поддержкой RTL
- **⚡ Оптимизировано для производительности**: Эффективное использование памяти и алгоритмы обработки

---

## 🌐 Browser Compatibility

- Chrome 60+ ✅
- Firefox 55+ ✅
- Safari 11+ ✅
- Edge 79+ ✅

## 📊 Performance Metrics

- **Encryption Speed**: ~50MB/s (typical hardware)
- **Memory Usage**: Optimized for large files with streaming
- **Storage Efficiency**: ~75% compression ratio average
- **DNA Sequence Length**: ~4x original file size (after compression)

## 🛠️ Development

### File Structure
```
src/
├── core/
│   ├── DNACodec.js (Advanced DNA encoding/decoding engine)
│   ├── AdvancedCryptoEngine.js (AES-GCM with PBKDF2)
│   └── AdvancedFileHandler.js (Optimized file processing)
├── storage/
│   └── StorageManager.js (IndexedDB with encryption metadata)
├── ui/
│   └── UIManager.js (Advanced UI management)
└── DNAEncryptionApp.js (Main application controller)
```

### Adding New Features
1. Create feature branch from `main`
2. Implement in appropriate module
3. Update UI components if needed
4. Add comprehensive tests
5. Update documentation
6. Submit pull request with detailed description

## 🔬 Technical Specifications

- **Supported File Types**: All formats up to 500MB
- **Encryption Algorithm**: AES-GCM-256 with authenticated encryption
- **Key Derivation**: PBKDF2 with SHA-256 (100,000 iterations)
- **Compression**: DEFLATE algorithm (pako.js implementation)
- **Storage**: IndexedDB for local persistence and metadata
- **DNA Alphabet**: Standard nucleotides (A, T, C, G)
- **UI Framework**: Vanilla JavaScript with modern ES6+ features
- **Styling**: CSS3 with Flexbox/Grid and animations

## 🚨 Security Considerations

- ⚠️ Never share your encryption passwords
- ⚠️ DNA sequences are not encrypted themselves - store securely
- ⚠️ Use strong, unique passwords for each file (minimum 8 characters)
- ⚠️ Verify checksums after decryption to ensure data integrity
- ⚠️ Clear browser cache if using on shared computers

## 📝 License

MIT License - see [LICENSE](LICENSE) file for details

## 👨‍💻 Developer

**Mezd** - Lead Developer & Creator
- GitHub: [@mezd](https://github.com/mezd)
- Email: <EMAIL>

## 🤝 Contributing

Contributions are welcome! Please read [CONTRIBUTING.md](CONTRIBUTING.md) first.

### Development Setup
1. Fork the repository
2. Clone your fork
3. Create a feature branch
4. Make your changes
5. Test thoroughly
6. Submit a pull request

## 📞 Support

For issues and questions:
- 🐛 **GitHub Issues**: [Create Issue](https://github.com/mezd/dna-encryption-platform/issues)
- 📧 **Email**: <EMAIL>
- 📚 **Documentation**: [Wiki](https://github.com/mezd/dna-encryption-platform/wiki)
- 💬 **Discussions**: [GitHub Discussions](https://github.com/mezd/dna-encryption-platform/discussions)

## 🏆 Acknowledgments

- Thanks to the bioinformatics community for DNA encoding inspiration
- Cryptography experts for security guidance
- Open source contributors for tools and libraries

---

<div align="center">

**Made with ❤️ by Mezd**

![DNA](https://img.shields.io/badge/🧬-DNA%20Encryption-blue?style=flat-square)
![Security](https://img.shields.io/badge/🔒-Military%20Grade-red?style=flat-square)
![Performance](https://img.shields.io/badge/⚡-High%20Performance-green?style=flat-square)

</div>

