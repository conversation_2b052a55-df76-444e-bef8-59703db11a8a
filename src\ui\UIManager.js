class UIManager {
    constructor() {
        this.progressContainer = null;
        this.progressFill = null;
        this.progressText = null;
        this.notificationContainer = null;
        this.messages = {
            processing: 'در حال پردازش...',
            encrypting: 'در حال رمزگذاری...',
            decrypting: 'در حال رمزگشایی...',
            compressing: 'در حال فشرده‌سازی...',
            decompressing: 'در حال باز کردن فشرده‌سازی...',
            encoding: 'در حال تبدیل به DNA...',
            decoding: 'در حال تبدیل از DNA...',
            complete: 'تکمیل شد',
            error: 'خطا',
            success: 'موفق',
            fileSelected: 'فایل انتخاب شد',
            encryptionComplete: 'رمزگذاری با موفقیت انجام شد!',
            decryptionComplete: 'رمزگشایی با موفقیت انجام شد! دانلود آغاز شد.',
            downloadStarted: 'دانلود آغاز شد'
        };
    }

    initialize() {
        this.progressContainer = document.getElementById('progressContainer');
        this.progressFill = document.getElementById('progressFill');
        this.progressText = document.getElementById('progressText');
        this.notificationContainer = document.getElementById('notificationContainer');
    }

    showProgress(text, progress = 0) {
        this.progressContainer.style.display = 'flex';
        this.progressText.textContent = text;
        this.updateProgress(progress);
    }

    updateProgress(progress) {
        this.progressFill.style.width = `${progress}%`;
        const baseText = this.progressText.textContent.split(' - ')[0];
        this.progressText.textContent = `${baseText} - ${Math.round(progress)}%`;
    }

    hideProgress() {
        this.progressContainer.style.display = 'none';
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.remove()">×</button>
        `;
        
        this.notificationContainer.appendChild(notification);
        
        // حذف خودکار بعد از ۵ ثانیه
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    showError(message) {
        this.showNotification(`❌ ${message}`, 'error');
    }

    showSuccess(message) {
        this.showNotification(`✅ ${message}`, 'success');
    }

    displayEncryptionResult(dnaSequence, encryptionId) {
        const resultContainer = document.getElementById('encryptionResult');
        if (!resultContainer) return;

        const compressionRatio = this.calculateCompressionRatio(dnaSequence);
        
        resultContainer.innerHTML = `
            <div class="encryption-result">
                <h4>🧬 نتیجه رمزگذاری</h4>
                <div class="result-stats">
                    <p><strong>طول توالی DNA:</strong> ${dnaSequence.length.toLocaleString('fa-IR')} نوکلئوتید</p>
                    <p><strong>نرخ فشرده‌سازی:</strong> ${compressionRatio}%</p>
                    <p><strong>شناسه رمزگذاری:</strong> ${encryptionId}</p>
                </div>
                <div class="dna-sequence">${dnaSequence}</div>
                <div class="result-actions">
                    <button onclick="navigator.clipboard.writeText('${dnaSequence}')" class="btn btn-small">
                        📋 کپی توالی DNA
                    </button>
                    <button onclick="window.uiManager.downloadDNA('${dnaSequence}', '${encryptionId}')" class="btn btn-small">
                        💾 دانلود فایل DNA
                    </button>
                    <button onclick="window.uiManager.generateQRCode('${dnaSequence}')" class="btn btn-small">
                        📱 تولید کد QR
                    </button>
                </div>
            </div>
        `;
    }

    calculateCompressionRatio(dnaSequence) {
        // محاسبه ساده نرخ فشرده‌سازی
        return Math.round((dnaSequence.length / (dnaSequence.length * 2)) * 100);
    }

    downloadDNA(dnaSequence, encryptionId) {
        const blob = new Blob([dnaSequence], { type: 'text/plain; charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `dna_encrypted_${encryptionId}.dna`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showSuccess('فایل DNA دانلود شد');
    }

    offerDownload(data, filename) {
        const blob = new Blob([data]);
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showSuccess(this.messages.decryptionComplete);
    }

    generateQRCode(dnaSequence) {
        // جای‌گذار برای تولید کد QR
        this.showNotification('قابلیت تولید کد QR به زودی اضافه خواهد شد!', 'info');
    }

    displayFileInfo(metadata) {
        const fileInfo = document.getElementById('fileInfo');
        if (!fileInfo) return;

        fileInfo.innerHTML = `
            <div class="file-info-card">
                <h4>📄 اطلاعات فایل</h4>
                <p><strong>نام:</strong> ${metadata.name}</p>
                <p><strong>اندازه:</strong> ${this.formatFileSize(metadata.size)}</p>
                <p><strong>نوع:</strong> ${metadata.type}</p>
                <p><strong>تاریخ تغییر:</strong> ${new Date(metadata.lastModified).toLocaleDateString('fa-IR')}</p>
            </div>
        `;
    }

    formatFileSize(bytes) {
        const sizes = ['بایت', 'کیلوبایت', 'مگابایت', 'گیگابایت'];
        if (bytes === 0) return '0 بایت';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        const size = Math.round(bytes / Math.pow(1024, i) * 100) / 100;
        return `${size.toLocaleString('fa-IR')} ${sizes[i]}`;
    }
}

// دسترسی سراسری به UIManager
window.uiManager = new UIManager();
