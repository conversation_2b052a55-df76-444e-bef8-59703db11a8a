@echo off
chcp 65001 >nul
title 🧬 DNA Encryption Platform - راه‌انداز خودکار توسط Mezd

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🧬 DNA Encryption Platform                    ║
echo ║                    راه‌انداز خودکار سیستم                     ║
echo ║                                                              ║
echo ║    ███╗   ███╗███████╗███████╗██████╗                        ║
echo ║    ████╗ ████║██╔════╝╚══███╔╝██╔══██╗                       ║
echo ║    ██╔████╔██║█████╗    ███╔╝ ██║  ██║                       ║
echo ║    ██║╚██╔╝██║██╔══╝   ███╔╝  ██║  ██║                       ║
echo ║    ██║ ╚═╝ ██║███████╗███████╗██████╔╝                       ║
echo ║    ╚═╝     ╚═╝╚══════╝╚══════╝╚═════╝                        ║
echo ║                                                              ║
echo ║                    توسعه‌یافته توسط Mezd                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js نصب نیست. لطفاً ابتدا آن را نصب کنید.
    echo 📥 دانلود از: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js شناسایی شد
echo.

:: Check if package.json exists
if not exist "package.json" (
    echo ❌ فایل package.json یافت نشد
    pause
    exit /b 1
)

:: Check if node_modules exists
if not exist "node_modules" (
    echo 📦 نصب وابستگی‌ها...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ❌ خطا در نصب وابستگی‌ها
        pause
        exit /b 1
    )
    echo ✅ وابستگی‌ها با موفقیت نصب شدند
) else (
    echo ✅ وابستگی‌ها از قبل نصب شده‌اند
)

echo.
echo 🚀 راه‌اندازی سرور...
echo.
echo 🌐 پلتفرم در آدرس زیر در دسترس خواهد بود:
echo    http://localhost:8000
echo.
echo 💡 برای توقف سرور Ctrl+C را فشار دهید
echo 👨‍💻 توسعه‌یافته توسط: Mezd
echo.

:: Start the server
npm start
