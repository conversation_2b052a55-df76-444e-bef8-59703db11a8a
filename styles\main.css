@import url('https://fonts.googleapis.com/css2?family=Vazirmatn:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Vazirmatn', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    direction: rtl;
    text-align: right;
}

.app-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.app-header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.app-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    font-weight: 700;
}

.app-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 400;
}

.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

section h2 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
}

.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #34495e;
}

.input-group input,
.input-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    transition: all 0.3s ease;
    direction: rtl;
    text-align: right;
}

.input-group input:focus,
.input-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-group textarea {
    min-height: 120px;
    resize: vertical;
    font-family: 'Courier New', monospace;
    direction: ltr;
    text-align: left;
}

.file-input-container {
    position: relative;
    margin-bottom: 20px;
}

.file-input-label {
    display: block;
    padding: 20px;
    border: 2px dashed #667eea;
    border-radius: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9ff;
    color: #667eea;
    font-weight: 500;
}

.file-input-label:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

#fileInput {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    font-family: inherit;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn:active {
    transform: translateY(0);
}

.btn-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.btn-secondary:hover {
    box-shadow: 0 10px 25px rgba(245, 87, 108, 0.3);
}

.progress-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.progress-bar {
    width: 300px;
    height: 20px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    color: white;
    font-size: 18px;
    font-weight: 500;
}

.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
    max-width: 400px;
}

.notification {
    background: white;
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    animation: slideIn 0.3s ease;
    border-right: 4px solid #667eea;
}

.notification-error {
    border-right-color: #e74c3c;
    background: #fdf2f2;
    color: #721c24;
}

.notification-success {
    border-right-color: #27ae60;
    background: #f0f9f4;
    color: #155724;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: inherit;
    margin-right: 10px;
}

.file-info-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.file-info-card h4 {
    margin-bottom: 10px;
    color: #495057;
    font-weight: 600;
}

.file-info-card p {
    margin: 5px 0;
    color: #6c757d;
    font-weight: 400;
}

.password-strength {
    margin-top: 5px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

.password-strength.weak {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.password-strength.weak::before {
    content: "ضعیف";
}

.password-strength.medium {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.password-strength.medium::before {
    content: "متوسط";
}

.password-strength.strong {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.password-strength.strong::before {
    content: "قوی";
}

.upload-section {
    grid-column: span 2;
}

.encryption-result {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.encryption-result h4 {
    color: #495057;
    margin-bottom: 15px;
    font-weight: 600;
}

.dna-sequence {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 15px;
    border-radius: 5px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    word-break: break-all;
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 15px;
    direction: ltr;
    text-align: left;
}

.result-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-small {
    padding: 8px 16px;
    font-size: 14px;
    width: auto;
    flex: 1;
    min-width: 120px;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .app-header h1 {
        font-size: 2rem;
    }
    
    section {
        padding: 20px;
    }
    
    .upload-section {
        grid-column: span 1;
    }
    
    .notification-container {
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .progress-bar {
        width: 250px;
    }
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

/* فونت‌های فارسی برای عناصر خاص */
input, textarea, button, label {
    font-family: 'Vazirmatn', sans-serif;
}

/* تنظیمات خاص برای متن‌های انگلیسی */
.dna-sequence, code, pre {
    font-family: 'Courier New', monospace;
    direction: ltr;
    text-align: left;
}

.developer-credit {
    margin-top: 10px;
    opacity: 0.8;
    font-size: 0.9rem;
}

.app-footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    color: white;
    backdrop-filter: blur(10px);
}

.app-footer p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

.app-footer strong {
    color: #ffd700;
    font-weight: 600;
}


